@extends('layouts.website')

@section('title', 'Artikel ' . $unit->nama_unit)

@section('content')
<div class="container py-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            @include('website.partials._sidebar')
        </div>

        <!-- Content -->
        <div class="col-md-9">
            <h2>Artikel {{ $unit->nama_unit }}</h2>
            <div class="row mt-4">
                @forelse ($articles as $article)
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            @if($article->image)
                                <img src="{{ asset('storage/' . $article->image) }}" 
                                     class="card-img-top" 
                                     alt="{{ $article->title }}">
                            @endif
                            <div class="card-body">
                                <h5 class="card-title">{{ $article->title }}</h5>
                                <p class="card-text">{{ $article->excerpt }}</p>
                                <a href="{{ route('website.artikel.show', $article->slug) }}" 
                                   class="btn btn-primary">
                                    ttBaca Selengkapnya
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <p class="text-center">Belum ada artikel.</p>
                    </div>
                @endforelse
            </div>
            
            <div class="d-flex justify-content-center mt-4">
                {{ $articles->links() }}
            </div>
        </div>
    </div>
</div>
@endsection