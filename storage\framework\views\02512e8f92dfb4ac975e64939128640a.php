<?php $__env->startSection('title', 'Daftar Ekstrakurikuler'); ?>

<?php $__env->startSection('content_header'); ?>
    <h1>Daftar Ekstrakurikuler</h1>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <a href="<?php echo e(route('admin.website.ekstrakurikuler.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Ekstrakurikuler
        </a>
    </div>
    <div class="card-body">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <table class="table table-bordered table-striped table-hover">
            <thead>
                <tr>
                    <th width="5%">No</th>
                    <th width="15%">Gambar</th>
                    <th width="20%">Judul</th>
                    <th>Deskripsi</th>
                    <th width="15%">Aksi</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $ekstrakurikuler; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td class="text-center"><?php echo e($key + 1); ?></td>
                    <td class="text-center">
                        <?php if($item->gambar): ?>
                            <img src="<?php echo e(Storage::url($item->gambar)); ?>" 
                                 alt="<?php echo e($item->nama); ?>" 
                                 class="img-thumbnail"
                                 style="max-width: 100px;">
                        <?php else: ?>
                            <span class="badge badge-secondary">No Image</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($item->nama); ?></td>
                    <td><?php echo e(Str::limit($item->deskripsi, 100)); ?></td>
                    <td class="text-center">
                        <a href="<?php echo e(route('admin.website.ekstrakurikuler.edit', $item->id)); ?>" 
                           class="btn btn-sm btn-warning">
                           <i class="fas fa-edit"></i> Edit
                        </a>
                        <form action="<?php echo e(route('admin.website.ekstrakurikuler.destroy', $item->id)); ?>" 
                              method="POST" 
                              style="display: inline-block;">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" 
                                    class="btn btn-sm btn-danger" 
                                    onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="5" class="text-center">Tidak ada data ekstrakurikuler</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<style>
    .table th, .table td {
        vertical-align: middle;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script>
    $(document).ready(function() {
        // Enable DataTables
        $('.table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
        });
    });
</script>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/website/ekstrakurikuler/index.blade.php ENDPATH**/ ?>