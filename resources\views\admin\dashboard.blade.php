@extends('layouts.admin')

@section('title', 'Dashboard')

@section('page_title', 'Dashboard Website')

@section('content')
<!-- Tambahkan card ucapan selamat datang -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-info">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-hand-wave mr-2"></i>
                    Selamat Datang di SIMAS Pelopor, {{ Auth::user()->name }}!
                </h5>
                <p class="card-text">
                    Sistem Informasi Manajemen Akademik Sekolah (SIMAS) Pelopor - Platform Digital Terpadu untuk Pengelolaan Akademik
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Utama -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $totalSiswa ?? 0 }}</h3>
                <p><PERSON><PERSON><PERSON> Sis<PERSON></p>
            </div>
            <div class="icon">
                <i class="fas fa-user-graduate"></i>
            </div>
            <a href="{{ route('peserta-didik.aktif') }}" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $totalGuru ?? 0 }}</h3>
                <p>Jumlah Guru</p>
            </div>
            <div class="icon">
                <i class="fas fa-chalkboard-teacher"></i>
            </div>
            <a href="{{ route('gtk.guru.index') }}" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $onlineUsers->count() }}</h3>
                <p>User Online</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
            <a href="#online-users" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
</div>

<!-- Grafik Siswa per Unit -->
@if(isset($siswaPerUnit) && auth()->user()->hasRole('admin'))
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Jumlah Siswa per Unit</h3>
            </div>
            <div class="card-body">
                <div class="chart">
                    <canvas id="siswaPerUnitChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Grafik Guru per Unit -->
@if(isset($guruPerUnit) && auth()->user()->hasRole('admin'))
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Jumlah Guru per Unit</h3>
            </div>
            <div class="card-body">
                <div class="chart">
                    <canvas id="guruPerUnitChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Tabel Siswa per Kelas -->
@if(isset($siswaPerKelas))
<div class="row">
    <div class="col-12">
        <h4 class="mb-3">Data Siswa per Kelas</h4>
    </div>
    
    @php
        // Kelompokkan data berdasarkan unit
        $siswaPerUnit = [];
        foreach($siswaPerKelas as $data) {
            $unit = isset($data['unit']) ? $data['unit'] : 'Tanpa Unit';
            if(!isset($siswaPerUnit[$unit])) {
                $siswaPerUnit[$unit] = [];
            }
            $siswaPerUnit[$unit][] = $data;
        }
    @endphp
    
    @foreach($siswaPerUnit as $unit => $dataKelas)
    <div class="col-md-6">
        <div class="card card-outline card-primary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-school mr-2"></i>
                    {{ $unit }}
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Kelas</th>
                                <th class="text-center">L</th>
                                <th class="text-center">P</th>
                                <th class="text-center">Jumlah</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $totalL = 0;
                                $totalP = 0;
                                $totalSiswa = 0;
                            @endphp
                            
                            @foreach($dataKelas as $data)
                            <tr>
                                <td>{{ $data['kelas'] }}</td>
                                <td class="text-center">{{ $data['laki_laki'] ?? 0 }}</td>
                                <td class="text-center">{{ $data['perempuan'] ?? 0 }}</td>
                                <td class="text-center">{{ $data['total'] }}</td>
                            </tr>
                            @php
                                $totalL += $data['laki_laki'] ?? 0;
                                $totalP += $data['perempuan'] ?? 0;
                                $totalSiswa += $data['total'];
                            @endphp
                            @endforeach
                            
                            <tr class="bg-light font-weight-bold">
                                <td>Total</td>
                                <td class="text-center">{{ $totalL }}</td>
                                <td class="text-center">{{ $totalP }}</td>
                                <td class="text-center">{{ $totalSiswa }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @endforeach
</div>
@endif

<!-- Kalender Pendidikan dan User Online (di bagian paling bawah) -->
<div class="row mb-4">
    <!-- Kalender Pendidikan (ukuran lebih kecil) -->
    <div class="col-md-7">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Kalender Pendidikan
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#modalTambahEvent">
                        <i class="fas fa-plus"></i> Tambah Event
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="calendarPendidikan" style="height: 400px;"></div>
            </div>
        </div>
    </div>
    
    <!-- User Online (berjajar dengan kalender) -->
    <div class="col-md-5" id="user-online-section">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">User Online (5 menit terakhir)</h3>
                
                @if(auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) && isset($units))
                <div class="card-tools">
                    <div class="d-flex">
                        <select id="filter-unit-online" class="form-control form-control-sm">
                            <option value="">Semua Unit</option>
                            @foreach($units as $unit)
                                <option value="{{ $unit->nama_unit }}">{{ $unit->nama_unit }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                @endif
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped" id="online-users-table">
                    <thead>
                        <tr>
                            <th>Nama</th>
                            <th>Unit</th>
                            <th>Terakhir Aktif</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($onlineUsers as $user)
                        <tr>
                            <td>{{ $user->name }}</td>
                            <td>{{ $user->unit ? $user->unit->nama_unit : '-' }}</td>
                            <td>
                                @if($user->last_activity)
                                    {{ \Carbon\Carbon::parse($user->last_activity)->diffForHumans() }}
                                @else
                                    -
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Event -->
<div class="modal fade" id="modalTambahEvent">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Tambah Event Kalender Pendidikan</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="formTambahEvent" action="{{ route('kalender.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="judul">Judul Event</label>
                        <input type="text" class="form-control" id="judul" name="judul" required>
                    </div>
                    <div class="form-group">
                        <label for="tanggal_mulai">Tanggal Mulai</label>
                        <input type="date" class="form-control" id="tanggal_mulai" name="tanggal_mulai" required>
                    </div>
                    <div class="form-group">
                        <label for="tanggal_selesai">Tanggal Selesai</label>
                        <input type="date" class="form-control" id="tanggal_selesai" name="tanggal_selesai" required>
                    </div>
                    <div class="form-group">
                        <label for="deskripsi">Deskripsi</label>
                        <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="warna">Warna</label>
                        <select class="form-control" id="warna" name="warna">
                            <option value="#007bff">Biru</option>
                            <option value="#28a745">Hijau</option>
                            <option value="#dc3545">Merah</option>
                            <option value="#ffc107">Kuning</option>
                            <option value="#6f42c1">Ungu</option>
                            <option value="#fd7e14">Oranye</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer justify-content-between">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detail Event -->
<div class="modal fade" id="modalDetailEvent">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="detailTitle">Detail Event</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Judul Event</label>
                    <p id="detailJudul" class="font-weight-bold"></p>
                </div>
                <div class="form-group">
                    <label>Tanggal</label>
                    <p id="detailTanggal" class="font-weight-bold"></p>
                </div>
                <div class="form-group">
                    <label>Deskripsi</label>
                    <p id="detailDeskripsi"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-danger" id="btnHapusEvent">Hapus</button>
                <button type="button" class="btn btn-primary" id="btnEditEvent">Edit</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal User Online (sesuaikan dengan kolom yang disederhanakan) -->
<div class="modal fade" id="onlineUsersModal" tabindex="-1" role="dialog" aria-labelledby="onlineUsersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="onlineUsersModalLabel">User Online (5 menit terakhir)</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <table class="table table-bordered table-striped" id="modal-online-users-table">
                    <thead>
                        <tr>
                            <th>Nama</th>
                            <th>Unit</th>
                            <th>Terakhir Aktif</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($onlineUsers as $user)
                        <tr>
                            <td>{{ $user->name }}</td>
                            <td>{{ $user->unit ? $user->unit->nama_unit : '-' }}</td>
                            <td>
                                @if($user->last_activity)
                                    {{ \Carbon\Carbon::parse($user->last_activity)->diffForHumans() }}
                                @else
                                    -
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('css')
@parent
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css" rel="stylesheet">
<style>
    .fc-event {
        cursor: pointer;
    }
    .fc .fc-toolbar.fc-header-toolbar {
        margin-bottom: 0.5em;
    }
    .fc .fc-toolbar-title {
        font-size: 1.25em;
    }
    .fc .fc-button {
        padding: 0.2em 0.5em;
        font-size: 0.9em;
    }
</style>
@endsection

@section('js')
@parent
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script>
$(function() {
    // Inisialisasi DataTable untuk tabel user online
    var onlineUsersTable = $('#online-users-table').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "pageLength": 5,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
    
    // Inisialisasi DataTable untuk tabel user online di modal
    var modalOnlineUsersTable = $('#modal-online-users-table').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
    
    // Filter user online berdasarkan unit
    $('#filter-unit-online').on('change', function() {
        var unit = $(this).val();
        onlineUsersTable.column(1) // Kolom unit (indeks 1 sekarang)
            .search(unit)
            .draw();
    });
    
    // Smooth scroll ke bagian user online saat klik "More info"
    $('.small-box.bg-warning a.small-box-footer').on('click', function(e) {
        e.preventDefault();
        $('html, body').animate({
            scrollTop: $('#user-online-section').offset().top - 70
        }, 500);
    });
    
    @if(isset($siswaPerUnit) && auth()->user()->hasRole('admin'))
    // Chart Siswa per Unit
    var ctxSiswa = document.getElementById('siswaPerUnitChart').getContext('2d');
    var dataSiswa = @json($siswaPerUnit);
    
    var labelsSiswa = dataSiswa.map(function(item) { return item.unit; });
    var valuesSiswa = dataSiswa.map(function(item) { return item.total; });
    
    var chartSiswa = new Chart(ctxSiswa, {
        type: 'bar',
        data: {
            labels: labelsSiswa,
            datasets: [{
                label: 'Jumlah Siswa',
                data: valuesSiswa,
                backgroundColor: 'rgba(60, 141, 188, 0.8)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
    @endif
    
    @if(isset($guruPerUnit) && auth()->user()->hasRole('admin'))
    // Chart Guru per Unit
    var ctxGuru = document.getElementById('guruPerUnitChart').getContext('2d');
    var dataGuru = @json($guruPerUnit);
    
    var labelsGuru = dataGuru.map(function(item) { return item.unit; });
    var valuesGuru = dataGuru.map(function(item) { return item.total; });
    
    var chartGuru = new Chart(ctxGuru, {
        type: 'bar',
        data: {
            labels: labelsGuru,
            datasets: [{
                label: 'Jumlah Guru',
                data: valuesGuru,
                backgroundColor: 'rgba(40, 167, 69, 0.8)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
    @endif
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Inisialisasi FullCalendar dengan ukuran yang lebih kecil
    var calendarEl = document.getElementById('calendarPendidikan');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,listMonth'
        },
        height: 400,
        contentHeight: 350,
        locale: 'id',
        events: "{{ route('kalender.events') }}",
        eventClick: function(info) {
            // Tampilkan detail event saat diklik
            $('#detailJudul').text(info.event.title);
            $('#detailTanggal').text(formatDateRange(info.event.start, info.event.end));
            $('#detailDeskripsi').text(info.event.extendedProps.description || '-');
            
            // Simpan ID event untuk keperluan edit/hapus
            $('#btnHapusEvent').data('id', info.event.id);
            $('#btnEditEvent').data('id', info.event.id);
            
            $('#modalDetailEvent').modal('show');
        }
    });
    calendar.render();
    
    // Format tanggal untuk tampilan detail
    function formatDateRange(start, end) {
        var startDate = new Date(start);
        var formattedStart = startDate.toLocaleDateString('id-ID', { 
            day: 'numeric', 
            month: 'long', 
            year: 'numeric' 
        });
        
        if (!end) return formattedStart;
        
        var endDate = new Date(end);
        var formattedEnd = endDate.toLocaleDateString('id-ID', { 
            day: 'numeric', 
            month: 'long', 
            year: 'numeric' 
        });
        
        return formattedStart + ' s/d ' + formattedEnd;
    }
    
    // Handler untuk tombol hapus event
    $('#btnHapusEvent').on('click', function() {
        var eventId = $(this).data('id');
        if (confirm('Apakah Anda yakin ingin menghapus event ini?')) {
            $.ajax({
                url: "{{ route('kalender.destroy', '') }}/" + eventId,
                type: 'DELETE',
                data: {
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    $('#modalDetailEvent').modal('hide');
                    calendar.refetchEvents();
                    toastr.success('Event berhasil dihapus');
                },
                error: function(xhr) {
                    toastr.error('Terjadi kesalahan saat menghapus event');
                }
            });
        }
    });
    
    // Handler untuk tombol edit event
    $('#btnEditEvent').on('click', function() {
        var eventId = $(this).data('id');
        window.location.href = "{{ route('kalender.edit', '') }}/" + eventId;
    });
});
</script>
@stop
