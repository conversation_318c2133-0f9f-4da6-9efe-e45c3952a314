<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content_header'); ?>
    <h1><?php echo $__env->yieldContent('page_title'); ?></h1>
    <?php if(config('app.debug')): ?>
        <div class="d-none">
            Current User: <?php echo e(auth()->user()->name); ?>

            Roles: <?php echo e(auth()->user()->getRoleNames()); ?>

            Permissions: <?php echo e(auth()->user()->getAllPermissions()->pluck('name')); ?>

        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->yieldContent('content'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" href="css/admin_custom.css">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
   <!-- <script src="https://cdn.ckeditor.com/ckeditor5/27.1.0/classic/ckeditor.js"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script> console.log('Hi!'); </script>
<?php $__env->stopSection(); ?>

<!-- Sidebar atau menu admin -->
<ul class="nav">
    <!-- ... other menu items ... -->
    
   
</ul>








<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/layouts/admin.blade.php ENDPATH**/ ?>